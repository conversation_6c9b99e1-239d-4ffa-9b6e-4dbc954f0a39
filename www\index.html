<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Urdu/Islamic Game - PhansiGhar 2.3</title>
  <meta name="description" content="Word Puzzle game, urdu game, learn urdu, arabic, islam, islamic, game, urdugini, urdugenie">
  <meta name="author" content="Naseem Amjad">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- CSS
  –––––––––––––––––––––––––––––––––––––––––––––––––– -->
  <link rel="stylesheet" href="css/normalize.css">
  <link rel="stylesheet" href="css/skeleton.css">
  <link rel="stylesheet" href="css/sumoselect.css">
  <link rel="stylesheet" href="css/jquery.stackbox.min.css">
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="css/jquery.modal.min.css" />

  <!-- Favicon
  –––––––––––––––––––––––––––––––––––––––––––––––––– -->
  <link rel="icon" type="image/png" href="rsc/favicon.png">

</head>
<body>

  <!-- Primary Page Layout
  –––––––––––––––––––––––––––––––––––––––––––––––––– -->
<div class="container centric game">

	<div class="row header" style="margin-top: 0">
		<h4>Phansi Ghar v2.3 - پھانسی گھر</h4>
	</div>

	<div class="row" style="margin-top: 0.5%">
		<div class="twelve columns">
			<span class="selector">Categories :</span> <select multiple="multiple" id="categories"></select>
		</div>			
	</div>
	<p><a href="#ex1" rel="modal:open"></a></p>
	<div class="row core centric" style="margin-top: 0.5%">
		<div class="">
			<canvas id="stickman">Your Browser does NOT support HTML5 Canvas tag</canvas>
		</div>
		<div class="">
			<div id="hold" dir="rtl"> </div>
			<p id="mylives"></p>
			<p id="clue"></p>				
				<p class="note" id="catagoryName"></p>
				</div>
		<div class="" id="keyboard">
			<div id="buttons" dir="rtl"></div>
		</div>
	</div>  
		<!-- Modal HTML embedded directly into document -->
		<div id="ex1" class="modal" style="text-align: center; background-image: url('rsc/checkmark.png');  background-repeat: no-repeat;">
		<p>جواب درست ھے۔</p>
		  <p>Your Answer is Correct !</p>
		  
		  <a href="#" rel="modal:close">Close</a>
		</div>
	<div class="row centric">
		<div class="control">
			<button id="hint" class="hintbutton">Hint</button>
			<button id="reset" class="simplebutton">Play again</button>
			<button id="howtoplay" class="simplebutton" onclick="" 
			href="#howtoplaybox" data-stackbox-close-button="false" data-stackbox-width="400" data-stackbox="true" data-stackbox-position="bottom" data-stackbox-anim-open="bounceInUp slow" data-stackbox-anim-close="hinge slow">How to play !</button>
		</div>
		<div id="howtoplaybox" style="display:none;">
			<div class="stackbox-body">Click on above Urdu / Arabic alphabets to guess the word from choosen category, or click hint to get a clue. See <a href="https://www.youtube.com/watch?v=0hxhdxz8Gi0" target="_blank">Video</a> on how to play Phansi Ghar Game.</div>
		</div>
		<div class="myText"><a href="https://www.facebook.com/urdugame/" target="_blank">Visit FaceBook Page</a> || <a href="https://www.twitter.com/urdujini/" target="_blank">Follow on Twitter</a></div>
	</div>

	<div class="row footer myText" style="margin-top: 0.5%">
	<div>
	    <p class="adp"><span class="adht">ADVERTISEMENT:</span><span id="myads"> 	</span></p>
</div>
		<br> &copy; Copyright<script>new Date().getFullYear()>2018&&document.write(" - "+new Date().getFullYear());</script>
		<a href="https://www.urdujini.com/privacy-policy.html" target="_blank">Privacy Policy</a>
	</div>
	
</div>


<script>function openGameWin(){ $("#ex1").modal(); } </script>
	<audio id="play" src="rsc/button.mp3"></audio>
	<script src="js/jquery-3.6.0.min.js"></script>
	<script src="js/jquery.multiselect.js"></script>
	<script src="js/jquery.stackbox.min.js"></script>
	<script src="js/sumosustain.js"></script>
	<script src="data.js"></script>
	<script src="js/index.js"></script>
	<script src="js/jquery.modal.min.js"></script>
	
<!-- Ads -->
<script>
var banners = [
  {
	"html": 'An <a href="https://play.google.com/store/apps/details?id=com.ajsoftpk.wordoc&hl=en&gl=US" target="_blank">English Game</a> by <a href="https://www.ajsoftpk.com/naseem_amjad/" target="_blank">Naseem Amjad</a>',
    "img": "checkmark.png",
    "url": "https://www.ajsoftpk.com",
    "weight": 6
  },
  {
	"html": 'Download <a href="https://play.google.com/store/apps/details?id=com.ajsoftpk.wordoc&hl=en&gl=US" target="_blank">WorDoc</a> Game by <a href="https://www.ajsoftpk.com/naseem_amjad/" target="_blank">Naseem Amjad</a>',
    "img": "checkmark.png",
    "url": "https://www.ajsoftpk.com",
    "weight": 2
  },
  {
	"html": 'Play <a href="https://play.google.com/store/apps/details?id=com.ajsoftpk.wordoc&hl=en&gl=US" target="_blank">Word Puzzle Game</a> by <a href="https://www.ajsoftpk.com/naseem_amjad/" target="_blank">Naseem Amjad</a>',
    "img": "checkmark.png",
    "url": "https://www.ajsoftpk.com",
    "weight": 1
  },
  {
	"html": '<a href="https://www.ajsoftpk.com/naseem_amjad/" target="_blank">Urdu Software</a> by <a href="https://www.ajsoftpk.com/naseem_amjad/" target="_blank">Naseem Amjad</a>',
    "img": "checkmark.png",
    "url": "https://www.ajsoftpk.com",
    "weight": 1
  },
  {
	"html": '<a href="https://www.ajsoftpk.com/" target="_blank">WebHosting</a> by <a href="https://www.ajsoftpk.com/#services" target="_blank">AJSoft</a>',
    "img": "checkmark.png",
    "url": "https://www.ajsoftpk.com",
    "weight": 2
  }
];



$.when( $.ready ).then(function() {
  var banner = randomBanner();
  // Add the banner to the page body.
  $('#myads').append("<span>"+banner["html"]+"</span></a>");
})

function randomBanner() {
    var totalWeight = 0, cummulativeWeight = 0, i;
    // Add up the weights.
    for (i = 0; i < banners.length; i++) {
        totalWeight += banners[i]["weight"];
    }
    console.log("Total weight: " + totalWeight);
    var random = Math.floor(Math.random() * totalWeight);
    // Find which bucket the random value is in.
    for (i = 0; i < banners.length; i++) {
        cummulativeWeight += banners[i]["weight"];
        if (random < cummulativeWeight) {
            return(banners[i]);
        }
    }
}

</script>


</body>
</html>
