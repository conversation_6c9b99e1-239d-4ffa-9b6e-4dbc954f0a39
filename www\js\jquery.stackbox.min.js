/*! stackbox v0.3.4 03.06.2015 */

!function(a,b){"use strict";function c(c){var d;if("string"==typeof c){if(a.fn.stackbox.globalSettings&&"string"==typeof a.fn.stackbox.globalSettings.NAMESPACE&&(d=a.fn.stackbox.globalSettings.NAMESPACE,b[d]&&a.isFunction(b[d][c])))return b[d][c];if(a.isFunction(b[c]))return b[c]}return a.isFunction(c)?c:function(){console.warn("Function not found",c)}}function d(b){b.preventDefault(),a(this).addClass("active").stackbox()}function e(a){27===a.keyCode&&(f(),a.preventDefault())}function f(){var a=m[m.length-1];a&&a.exitStackbox(!0)}function g(a){var b,c,d,e=a.parents(".stackbox");if(e.length)for(b=e.data("stackboxIndex"),d=m.length-1;d>=0;d--)d>=b&&(c=m[d],c&&c.exitStackbox(!0))}function h(c){var d,e,f,g,h,i,j,k=c.target,l=a(k),n=a(b),o=[],p=r,q=s;if(1!==c.which)return!1;for(a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.stackboxclass&&(p=a.fn.stackbox.globalSettings.stackboxclass),a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.wrapperclass&&(q=a.fn.stackbox.globalSettings.wrapperclass);k&&k!==document.body&&k!==document;){if(l=a(k),l.data("closeStackbox")===!0)return!1;if(l.hasClass(p)){for(e=l.data("stackboxIndex"),h=m.length-1;h>=0;h--)h>e&&(d=m[h],d&&d.exitStackbox(!0));return!0}k=k.parentNode}if(l=a(c.target),i=n.outerWidth()-c.clientX<l[0].offsetWidth-l[0].clientWidth,j=n.outerHeight()-c.clientY<l[0].offsetHeight-l[0].clientHeight,i||j)return!1;if(1===m.length&&(d=m[0],d&&"none"===d.$wrapper[0].style.pointerEvents))return d.exitStackbox(!0),!0;if(a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.whitelist)for(h=0;h<a.fn.stackbox.globalSettings.whitelist.length;h++)if(l.parents("."+a.fn.stackbox.globalSettings.whitelist[h]).length)return!0;if(f=l.data("stackboxGroup")){for(h=m.length-1;h>=0;h--)d=m[h],d&&(g=d.$stackbox.data("stackboxGroup"),g===f&&o.push(d));if(o.length)if(1===o.length&&o[0].options.closeOnBackdrop===!0)o[0].exitStackbox(!0);else for(h=0;h<o.length-1;h++)o[h].options.closeOnBackdrop===!0&&o[h].exitStackbox(!0)}else for(h=m.length-1;h>=0;h--)d=m[h],d&&d.exitStackbox(!0)}function i(){var a,b;for(b=0;k>b;b++)a=m[b],a&&a.updatePosition(a)}function j(){i()}var k=0,l=0,m=[],n=[],o=20,p=34,q=10,r="stackbox",s="stackbox-wrapper",t=!1,u="animationend webkitAnimationEnd MSAnimationEnd oAnimationEnd",v={init:function(b,d){var e,f;this.options=a.extend({},a.fn.stackbox.settings,b,a.fn.stackbox.globalSettings),this.previousDimensions={width:0,height:0},"fixed"===this.options.position&&(this.options.position="absolute"),this.loadable=!1,null!==this.options.nextTo?this.$offspring=a(this.options.nextTo):void 0!==d?this.$offspring=a(d):this.options.position="absolute","auto"===this.options.backdrop&&(this.options.backdrop="absolute"===this.options.position?!0:!1),this.createDOMElements(),e=a(d).attr("href"),this.options.content===!1&&e&&(this.options.content=e),this.addContent(),this.loadable!==!0&&this.createCloseButton(),this.createArrow(),this.addEventListeners(),this.created=!0,c(this.options.beforeOpen)(this.$offspring,this),a(document).trigger("beforeOpen.stackbox",[this.$offspring,this]),this.loadable===!0?this.loadAjax(this.options.content):(this.updatePosition(),f=function(){this.$stackbox.removeClass("animated "+this.options.animOpen),this.afterOpen(!0)}.bind(this),t?this.$stackbox.addClass("animated "+this.options.animOpen).off(u).on(u,f):f())},afterOpen:function(b){b===!0&&this.updatePosition(),this.autoScroll(),c(this.options.afterOpen)(this.$stackbox,this.$offspring,this),a(document).trigger("afterOpen.stackbox",[this.$stackbox,this.$offspring,this])},createArrow:function(){return void 0===this.$offspring||"absolute"===this.options.position?void(this.$arrow=!1):(a('<div class="stackbox-arrow"></div>').appendTo(this.$stackbox),this.$arrow=this.$stackbox.find(".stackbox-arrow"),this.arrowWidth=this.$arrow.outerWidth(),void(this.arrowHeight=this.$arrow.outerHeight()))},createCloseButton:function(){var b;this.options.closeButton===!0&&(b=a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.closeButtonIcon?a.fn.stackbox.globalSettings.closeButtonIcon:this.options.closeButtonIcon,this.$closeButton=a('<div class="stackbox-close" data-close-stackbox="true"><button type="button" class="close">'+b+"</button></div>"),this.$stackbox.prepend(this.$closeButton))},createDOMElements:function(){var b,c,d,e={display:"block"},f=s,g=r,i=this.options.mainWrapperClass,j="";a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.stackboxclass&&(g=a.fn.stackbox.globalSettings.stackboxclass),this.$offspring&&(b=this.$offspring.parents("."+g)),a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.mainWrapperClass&&(i=a.fn.stackbox.globalSettings.mainWrapperClass,a.fn.stackbox.globalSettings.mainwrapperextraclass&&(j+=" "+a.fn.stackbox.globalSettings.mainwrapperextraclass)),1===k?this.$wrapperWrapper=a("<div></div>").addClass(i+j).appendTo(a(document.body)):b&&(this.$wrapperWrapper=b.parents("."+i)),1===k||this.options.backdrop===!0?(this.$wrapper=a("<div></div>").addClass(f).css("z-index",9900+k).appendTo(this.$wrapperWrapper),1===k&&this.options.backdrop!==!0&&this.$wrapper.css({overflow:"hidden","pointer-events":"none"}),l++,this.$wrapper.data("stackboxGroup",l),a(document).off("mousedown.stackbox").on("mousedown.stackbox",h)):b&&(this.$wrapper=b.parent()),this.hadNoScroll=a("html").hasClass(this.options.noscrollClass),this.options.backdrop===!0&&this.$wrapper.addClass("stackbox-backdrop"),"absolute"===this.options.position&&a("html").addClass(this.options.noscrollClass),"auto"===this.options.width&&(e.width="auto"),this.$stackbox=a("<div></div>").addClass(g).css(e).appendTo(this.$wrapper),this.options.closeOnBackdrop===!0?this.$wrapper.addClass("stackbox-close-on-backdrop"):this.$wrapper.removeClass("stackbox-close-on-backdrop"),c=k-1,this.stackboxIndex=c,this.$stackbox.data("stackboxIndex",c),d=this.$wrapper.data("stackboxGroup"),this.stackboxGroup=d,this.$stackbox.data("stackboxGroup",d)},addEventListeners:function(){if(this.options.autoadjust===!0){var a=function(){(this.$stackbox.outerWidth()!==this.previousDimensions.width||this.$stackbox.outerHeight()!==this.previousDimensions.height)&&this.updatePosition(),this.timeoutID=b.setTimeout(a,250)}.bind(this);a()}this.$stackbox.on("click",'[data-close-stackbox="true"]',{stackbox:this.$stackbox},this.closeClick)},closeClick:function(b){var c,d,e=b.data.stackbox,f=e.data("stackboxIndex"),g=a(this);for(b.preventDefault(),b.stopPropagation(),d=k-1;d>=0;d--)d>=f&&(c=m[d],c&&(g.data("stackbox")===!0?c.exitStackbox(!1,function(){g.stackbox()}):c.exitStackbox()))},loadAjax:function(b){var d,e,f,g,h;"string"==typeof b&&(b={url:b,dataType:"html",type:this.options.requestType}),this.$arrow&&(this.$arrow=this.$stackbox.find(".stackbox-arrow").detach()),h=a.fn.stackbox.globalSettings&&a.fn.stackbox.globalSettings.spinnerClass?a.fn.stackbox.globalSettings.spinnerClass:this.options.spinnerClass,this.$stackbox.html('<div style="padding: 40px; text-align: center;"><div class="'+h+'"></div></div>'),this.$arrow&&this.$arrow.appendTo(this.$stackbox),this.updatePosition(),null!==this.options.queryParams&&("string"==typeof this.options.queryParams?b.data=a.parseJSON(this.options.queryParams.replace(/'/g,'"')):"object"==typeof this.options.queryParams&&(b.data=this.options.queryParams)),this.$arrow&&this.setArrowPos(),this.ajaxRequest=a.ajax(b),d=function(a){this.$stackbox.hide(),this.$stackbox.html(a),this.$arrow&&this.$arrow.appendTo(this.$stackbox)}.bind(this),e=function(a,d){var e,f=this.options.requestFailed;e=a&&a.responseText&&a.responseText.length>0?a.responseText:this.options.requestFailed,b&&b.url&&(f+=" Url: "+b.url),"abort"!==d&&(console.warn(f),c(this.options.onError)(this,a,d),this.$stackbox.trigger("onError.stackbox",[this,a,d])),this.$stackbox.html('<div style="padding: 10px;">'+e+"</div>")}.bind(this),f=function(){this.createCloseButton(),this.updatePosition(),this.updatePosition(),g=function(){this.$stackbox.removeClass("animated "+this.options.animOpen),this.afterOpen()}.bind(this),t?this.$stackbox.show(0).addClass("animated "+this.options.animOpen).off(u).on(u,g):this.$stackbox.fadeIn(200,function(){g()})}.bind(this),this.ajaxRequest.fail(e),this.ajaxRequest.done(d),this.ajaxRequest.always(f)},addContent:function(){var b,c=this.options.content,d=c.match(/^https?:\/\//)||"/"===c.charAt(0);"#"===c.charAt(0)?(b=a(c),b.length>0?(b=b.children(),this.options.clone?b.clone(!0,!0).appendTo(this.$stackbox):(this.options.returnContent&&(this.returnContent=a(b[0]).parent()),0===b.length?this.$stackbox.html('<div style="padding: 10px;">No content in element: "'+c+'"</div>'):b.appendTo(this.$stackbox))):this.$stackbox.html('<div style="padding: 10px;">Could not find element: "'+c+'"</div>')):d?this.loadable=!0:this.$stackbox.html(c)},adjustToScroll:function(c){return c.left-=a(b).scrollLeft(),c.left+=this.$wrapper.scrollLeft(),c.top-=a(b).scrollTop(),c.top+=this.$wrapper.scrollTop(),c.left=Math.round(c.left),c.top=Math.round(c.top),c},positionBottom:function(a){var b={};return b.left=this.$offspring.offset().left+this.$offspring.outerWidth()/2-a.stackboxWidth/2,b.top=a.marginY+(this.$offspring.offset().top+this.$offspring.outerHeight()),this.arrowDirection="up",this.adjustToScroll(b)},positionTop:function(a){var b={};return b.left=this.$offspring.offset().left+this.$offspring.outerWidth()/2-a.stackboxWidth/2,b.top=-a.marginY+(this.$offspring.offset().top-a.stackboxHeight),this.arrowDirection="down",this.adjustToScroll(b)},positionLeft:function(a){var b={};return b.left=this.$offspring.offset().left-a.stackboxWidth-a.marginX,b.top=this.$offspring.offset().top+(this.$offspring.outerHeight()-a.stackboxHeight)/2,this.arrowDirection="right",this.adjustToScroll(b)},positionRight:function(a){var b={};return b.left=this.$offspring.offset().left+this.$offspring.outerWidth()+a.marginX,b.top=this.$offspring.offset().top+(this.$offspring.outerHeight()-a.stackboxHeight)/2,this.arrowDirection="left",this.adjustToScroll(b)},positionAbsolute:function(c){var d={},e=a(b);return d.left=e.width()/2-c.stackboxWidth/2+e.scrollLeft(),d.top=e.outerHeight()/2-c.stackboxHeight/2+e.scrollTop(),this.arrowDirection=!1,this.adjustToScroll(d)},updatePosition:function(){var c,d,e,f,g,h=a(b).width();return this&&this.options?("auto"!==this.options.width?(c=this.calcStackboxWidth(),this.options.respectBrowserWidth&&c+p>h&&(c=h-p),this.$stackbox.width(c)):c=this.$stackbox.width(),d=this.$stackbox.height(),e={marginX:this.options.marginX,marginY:this.options.marginY,stackboxWidth:c,stackboxHeight:d},f="position"+this.options.position.charAt(0).toUpperCase()+this.options.position.substring(1),this.$offspring&&this.$offspring.selector&&(this.$offspring=a(this.$offspring.selector)),this[f]||(console.warn('stackbox: Unknown position method "'+f+'"'),f="positionAbsolute"),g=this[f](e),this.$stackbox.css(g),a.extend(e,{left:parseInt(this.$stackbox.css("left"),10),top:parseInt(this.$stackbox.css("top"),10),windowWidth:h,windowHeight:a(b).height(),width:this.$stackbox.outerWidth(),height:this.$stackbox.outerHeight()}),this.adjustToWindow(e),void(this.$arrow&&this.setArrowPos())):!1},calcArrowLeft:function(){var c,d=parseInt(this.$stackbox.css("left"),10),e=this.$stackbox.outerWidth(),f=this.$offspring.offset().left,g=this.$offspring.outerWidth(),h=f+g/2,i=this.arrowWidth,j=i/2,k=6,l=e-i-6;return c=h-d-j,c-=a(b).scrollLeft(),c+=this.$wrapper.scrollLeft(),"bottom"===this.options.position&&this.$closeButton&&(l-=this.$closeButton.outerWidth()-Math.abs(parseInt(this.$closeButton.css("right"),10))),k>c?c=k:c>l&&(c=l),c},calcArrowTop:function(){var c,d=parseInt(this.$stackbox.css("top"),10),e=this.$stackbox.outerHeight(),f=this.$offspring.offset().top,g=this.$offspring.outerHeight(),h=f+g/2,i=this.arrowHeight,j=i/2,k=6,l=e-i-6;return c=h-d-j,c-=a(b).scrollTop(),c+=this.$wrapper.scrollTop(),k>c?c=k:c>l&&(c=l),c},setArrowPos:function(){var a,b;this.$arrow.removeClass("top bottom left right"),"up"===this.arrowDirection?(a=this.calcArrowLeft()+this.arrowWidth/2,b=-(this.arrowHeight/2),this.$arrow.css({left:Math.round(a),top:Math.round(b)}).addClass("bottom")):"down"===this.arrowDirection?(a=this.calcArrowLeft()+this.arrowWidth/2,b=this.$stackbox.height(),this.$arrow.css({left:Math.round(a),top:Math.round(b)}).addClass("top")):"right"===this.arrowDirection?(a=this.$stackbox.width(),b=this.calcArrowTop()+this.arrowHeight/2,this.$arrow.css({left:Math.round(a),top:Math.round(b)}).addClass("left")):"left"===this.arrowDirection&&(a=-(this.arrowWidth-this.arrowWidth/2),b=this.calcArrowTop()+this.arrowHeight/2,this.$arrow.css({left:Math.round(a),top:Math.round(b)}).addClass("right"))},adjustToWindow:function(c){var d,e;this.options.autoAdjust===!0&&("left"===this.options.position||"right"===this.options.position?(c.top<q||c.left<o||c.left+c.width>c.windowWidth-10)&&(e=this.positionBottom(c),this.$stackbox.css(e)):"absolute"===this.options.position?c.top<q&&this.$stackbox.css("top",q):c.top<q?(e=this.positionBottom(c),this.$stackbox.css(e)):c.top+c.height+a(b).scrollTop()>a(document).height()&&(a("html").hasClass(this.options.noscrollClass)||c.top+c.height>a(b).height()&&(this.$wrapper.css("overflow","auto"),this.$wrapper.css("pointer-events","all"),a("body").css("overflow","hidden")))),c.left<o?this.$stackbox.css("left",o):c.left+c.stackboxWidth>c.windowWidth-p&&(d=Math.max(o,c.windowWidth-p-c.stackboxWidth),this.$stackbox.css("left",d))},calcStackboxWidth:function(){var c=a(b).width(),d=this.options.width,e=this.options.maxWidth===!1?1e4:parseInt(this.options.maxWidth,10),f=this.options.minWidth===!1?0:parseInt(this.options.minWidth,10);return"string"==typeof d&&(d=parseInt(this.options.width,10),-1!==this.options.width.indexOf("%")&&(d=c*(d/100))),d>e&&(d=e),d>c-p&&(d=c-p),f>d&&(d=f),d},autoScroll:function(){if(!this.options.autoScroll||"absolute"===this.options.position)return!1;var c,d,e=this.$stackbox.offset().top,f=this.$wrapper.scrollTop(),g=a(b).height(),h=a(b).scrollTop(),i=this.$stackbox.height(),j=20,k={duration:this.options.scrollSpeed,easing:this.options.scrollEasing},l=m[0],n=!1;for(l&&"absolute"!==l.options.position&&(n=!0),d=1;d<m.length;d++)"absolute"===m[d].options.position&&(n=!1);n?h>e?a("body,html").animate({scrollTop:e-j},k):e-h+i>g&&(c=i>g?e-j:e+i-g+j,a("body,html").animate({scrollTop:c},k)):h>e+f?this.$wrapper.animate({scrollTop:e-j},k):e-h+i>g&&(c=i>g?e-h+f-j:e-h+f+i-g+j,this.$wrapper.animate({scrollTop:c},k))},exitStackbox:function(b,d){var e,f;c(this.options.beforeClose)(this.$stackbox,this.$offspring,this),this.$stackbox.trigger("beforeClose.stackbox",[this.$stackbox,this.$offspring,this]),this.ajaxRequest&&this.ajaxRequest.abort(),m.length&&(f=m[m.length-2],f&&(f.options.closeOnBackdrop===!0?f.$wrapper.addClass("stackbox-close-on-backdrop"):f.$wrapper.removeClass("stackbox-close-on-backdrop"))),b!==!0&&this.options.animClose?(e=function(){this.$offspring&&this.$offspring.removeClass("active"),a.isFunction(d)?(this.cleanUp(),d.call(this)):this.cleanUp()}.bind(this),t?this.$stackbox.addClass("animated "+this.options.animClose).off(u).on(u,e):this.$stackbox.fadeOut(200,function(){e()})):(this.cleanUp(),this.$offspring&&this.$offspring.removeClass("active"),a.isFunction(d)&&d.call(this))},cleanUp:function(){this.$arrow&&this.$arrow.remove(),this.$closeButton&&this.$closeButton.remove(),this.hadNoScroll===!1?a("html").removeClass(this.options.noscrollClass):a("html").addClass(this.options.noscrollClass),b.clearTimeout(this.timeoutID),m.pop(),k=m.length,n.pop(),c(this.options.afterClose)(this.$stackbox,this.$offspring,this),this.$stackbox.trigger("afterClose.stackbox",[this.$stackbox,this.$offspring,this]),this.options.returnContent===!0&&void 0!==this.returnContent&&this.$stackbox.children().appendTo(this.returnContent),this.$stackbox.remove(),0===k&&(this.$wrapperWrapper.remove(),a("body").css("overflow","auto")),0===this.$wrapper.children().length&&(this.$wrapper.remove(),l--)},toString:function(){return this.created===!0?"stackbox [#"+this.stackboxIndex+", g"+this.stackboxGroup+"]":"stackbox [uninitialized]"}};a.fn.stackbox=function(b){return"close"===b?(g(this),!0):"updatePosition"===b?(i(),!0):this.each(function(){for(var c=0;c<n.length;c++)if(this===n[c])return console.warn("Stackbox already initialized on element!"),console.dir(this),!1;n.push(this);var d,e,f,g,h=a(this).data(),i={};for(d in h)h.hasOwnProperty(d)&&0===d.indexOf("stackbox")&&(e=d.substr(8),e=e.charAt(0).toLowerCase()+e.substr(1),e&&(i[e]=h[d]));b=a.extend(i,b),g=Object.keys(b);for(f in g)g.hasOwnProperty(f)&&(g[f]in a.fn.stackbox.settings||console.info('Stackbox option "'+g[f]+'" is invalid.'));m.push(Object.create(v)),k=m.length,m[k-1].init(b,this)})},a.fn.stackbox.settings={content:!1,width:"auto",maxWidth:!1,minWidth:!1,respectBrowserWidth:!0,scrollSpeed:600,scrollEasing:"easeOutCirc",backdrop:"auto",closeOnBackdrop:!0,position:"bottom",marginX:15,marginY:5,nextTo:null,animOpen:"fadeIn",animClose:"fadeOut",mainWrapperClass:"stackboxes",noscrollClass:"noscroll",closeButtonIcon:"&#x2716;",spinnerClass:"loading-spinner",autoAdjust:!0,autoScroll:!0,queryParams:null,requestType:"GET",clone:!1,returnContent:!0,closeButton:!0,requestFailed:"Request failed. Please try again.",beforeOpen:a.noop,afterOpen:a.noop,beforeClose:a.noop,afterClose:a.noop,onError:a.noop},a(document).on("close.stackbox",f).on("click","[data-stackbox]",d),a(b).on("resize",i).on("keydown",e).on("scroll",j),a.extend(a.easing,{easeOutBack:function(a,b,c,d,e,f){return void 0===f&&(f=1.70158),d*((b=b/e-1)*b*((f+1)*b+f)+1)+c},easeOutCirc:function(a,b,c,d,e){return d*Math.sqrt(1-(b=b/e-1)*b)+c}}),function(){var a="animation",b="",c="Webkit Moz O ms Khtml".split(" "),d="";if(void 0!==document.documentElement.style.animationName&&(t=!0),t===!1)for(var e=0;e<c.length;e++)if(void 0!==document.documentElement.style[c[e]+"AnimationName"]){d=c[e],a=d+"Animation",b="-"+d.toLowerCase()+"-",t=!0;break}}()}(jQuery,window);
//# sourceMappingURL=jquery.stackbox.min.js.map