.footer, .centric {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

.gateback{

}

.game{
	margin-top:0.5%;
	border:	1px solid;
}


body {
  color: #000;
  background-color: #fffbe5;
  
  background: url("../rsc/photo-background.jpg") no-repeat center center fixed; 
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  background-size: 100% 100%;
}

.container {
	padding: 1px;
}

.header {
  color: #fff;
  font-family: "HelveticaNeue-Light", "Helvetica Neue Light", "Helvetica Neue",
    Helvetica, Arial, "Lucida Grande", sans-serif; /* background-attachment: fixed; */ /* background: #0d86b3; */ /* Old browsers */
  background: -moz-linear-gradient(
    top,
    #0d86b3 0%,
    #11a9e2 100%
  ); /* FF3.6-15 */ /* background: -webkit-linear-gradient(top,  #0d86b3 0%,#11a9e2 100%); */ /* Chrome10-25,Safari5.1-6 */
  background: hsl(
    220deg 40% 10%
  ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0d86b3', endColorstr='#11a9e2',GradientType=0 ); /* IE6-9 */
}

h1,
h2,
h3,
h4 {
  font-family: "Roboto", sans-serif;
  font-weight: 50;
  /*font-weight: bold; */
  /* margin: 1px 0; */
  line-height: 1;
  padding-top: 20px;
}

h1 {
  font-size: 1em;
  font: bold 30px/1 "Helvetica Neue", Helvetica, Arial, sans-serif;
text-shadow: 0 1px 0 #bbb,
               0 2px 0 #c9c9c9;

}

.core{

}

canvas {
  background-color: #CCCCEE;
  /* border: #0a0 dashed 2px; */
  padding: 5px;
  left: 0px;
  color: #fff;
}

#stickman {
  margin: 0px 0 0;
  height: 100px;
  width: 200px;
}

.simplebutton{
	
	background-color: #ffffff;
}

.hintbutton{
	color: #CC8800;
	background-color: #000000;
}

.livetext{
	color: #FF1122;
}

#buttons {
  margin: auto;
  text-align: center;
  
}

#alphabet {
  max-width: 784px;
  margin: auto auto 2.5rem;
}

#keyboard{

}



#alphabet:after {
  content: "";
  display: table;
  clear: both;
}

#alphabet li {
  float: right;
  /* border-radius: 50%; */
  margin: 0 12px 12px 0;
  list-style: none;
  width: 12px;
  height: 24px;
  padding: 8px 16px;
  background: url("../rsc/button.jpg");
  color: #fff;
  cursor: pointer;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 50%;
  -khtml-border-radius: 5px;
  /* border: solid 1px #000; */
  font-size: 1.2em;
  font-weight: bold;
  background-size: cover;
  box-shadow: 0 2px 4px #2225;
  text-align: center;
  line-height: 1.2em;
}
#alphabet li:hover {
  background: #2227;
  /* border: solid 1px #000; */
  /* color: #fff; */
}

#my-word {
  color: #000;
  margin: 0;
  display: block;
  padding: 0;
  display: block;
}

#my-word li {
  position: relative;
  list-style: none;
  margin: 0;
  display: inline-block;
  padding: 0 5px;
  font-size: 2em;
}

.active {
  opacity: 0.4;
  filter: alpha(opacity=40);
  -moz-transition: all 1s ease-in;
  -moz-transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  cursor: default;
}
.active:hover {
  -moz-transition: all 1s ease-in;
  -moz-transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  opacity: 0.4;
  filter: alpha(opacity=40);
  -moz-transition: all 1s ease-in;
  -moz-transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
}

#mylives {
  font-size: 1.4em;
  text-align: center;
  display: block;
  /* color: #ea5090; */
}

.note {
  font-size: 1.2em;
  text-align: center;
  display: block;
  margin: 0;
  /* color: #808080; */
  padding-top: 4px;
  padding-bottom: 4px;
}

#clue {
  font-size: 1.4em;  
}

.jump{
   clear: both;
}

.ans  {
	text-decoration: none; 
	color:#ff1e10; 
	margin-top: 0.5%;

}

.ctg  {
	text-decoration: none; 
	color:#590; 
	margin-top: 0.5%;

}

span {
  color: rgba(0, 0, 0, 0.9);
  font-weight: bold;
}

.selector {
  min-width: 90px;
  vertical-align: 8px;
  display: inline-block;
}

.footer{ 
	margin: 20px 0 0 0;
}


.SumoSelect .select-all {
  padding: 5px 0px 5px 30px;
  height: 20px;
}

.close {padding:0;line-height:0;}

.myText {
  font-size: small;
}

a {
	color: darkblue;
}

.adp {
	font-style: italic;
	border: dotted;
	background-color: skyblue;	
}

.adht {
	color: red;
	font-size: medium;
}

@media (max-width: 767px) {

}

@media (max-width: 480px) {
	#alphabet { padding: 0 0 0 10px; }
	#alphabet li {
		margin: 0 4px 4px 0;
		list-style: none;
		width: 10px;
		height: 25px;
		padding: 4px 12px;
		font-size: 1.2em;
	}
}